package com.dcai.aixg.dto.task;

import com.dcai.aixg.dto.BrokerDTO;
import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.dcai.aixg.domain.task.Task")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "任务")
public class TaskDTO extends BaseDTO<TaskDTO> {

    public TaskDTO(Long id) {
        super(id);
    }

    @QueryField
    @Schema(description = "经纪人")
    private BrokerDTO broker;

    @QueryField
    @Schema(description = "类型")
    private TaskDTO.Type type;

    @QueryField
    @Schema(description = "状态")
    private TaskDTO.Status status;

    @QueryField
    @Schema(description = "报告类型")
    private TaskDTO.ReportType reportType;
    
    @Getter
    public enum Type implements TitleEnum {
    	REPORT("报告"),
    	WRITE("文章");
        private final String title;

        Type(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
    
    @Getter
    public enum SubType implements TitleEnum {
    	ORIGIN("原始"),
    	REDNOTE("小红书"),
    	WECHAT("微信");
        private final String title;

        SubType(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @Getter
    public enum Status implements TitleEnum {
        WAIT("待执行"),
        ING("生成中"),
        DONE("已生成"),
        FAIL("生成失败"),
        UNKNOW("未知");
        private final String title;

        Status(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
    
    @Getter
    public enum ReportType implements TitleEnum {
    	COMPARE("优劣势对比"),
    	MARKET_POLICY_ANALYSIS("市场政策分析"),
    	SUPPORTING_FACILITY_RESEARCH("城市配套深度调研"),
    	PRICE_ASSESSMENT_REPORT("二手房价格评测");
        private final String title;

        ReportType(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
    
}
