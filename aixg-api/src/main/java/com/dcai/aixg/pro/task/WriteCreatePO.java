package com.dcai.aixg.pro.task;

import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.pro.LaunchFlowPO;
import com.ejuetc.commons.base.exception.BusinessException;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class WriteCreatePO {
	
	private Long userId;
	
	//文章类型 1:小区专家 2:区域板块专家 3:房源百科专家 4:市场政策解读专家 5:城市置业解读专家 6:二手房估价专家 11:政策解读专家 12:价值评测专家 13:房产生活专家 14:房源专家 15:月度市场专家 16:行业政策分析 17:价值评测报告
	@NotNull(message = "请输入文章类型")
    @Schema(name = "writeType", description = "文章类型 11:政策解读专家 12:价值评测专家 13:房产生活专家 14:房源专家 15:月度市场专家 16:行业政策分析 17:价值评测报告")
    private String writeType;
	
    @Schema(name = "houseBaseInfo", description = "房源信息(价值评测报告使用)")
	private WriteCreateHouseBaseInfoPO houseBaseInfo;
    
    @Schema(name = "userInfo", description = "报告个人信息")
	private WriteCreateUserInfoPO userInfo;
	
    @Schema(name = "cityName", description = "城市名")
    private String cityName;
	
    @Schema(name = "areaName", description = "板块名")
    private String areaName;
	
    @Schema(name = "policy", description = "政策")
    private String policy;
	
    @Schema(name = "compareType", description = "对比类型 1:房源对比 2:小区比对 3:区域板块比对")
    private String compareType;
	
    @Schema(name = "houseCompareA", description = "对比房源1（/小区/区域板块）")
	private JSONObject houseCompareA;
	
    @Schema(name = "houseCompareB", description = "对比房源2（房源/小区/区域板块）")
	private JSONObject houseCompareB;
	
    @Schema(name = "compareA", description = "对比小区/区域板块1")
	private WriteCreateComparePO compareA;
	
    @Schema(name = "compareB", description = "对比小区/区域板块2")
	private WriteCreateComparePO compareB;
	
    @Schema(name = "lifeInfo", description = "猜你想说 多个标签用,分割")
    private String lifeInfo;
    
//	//@NotNull(message = "请输入区域名")
//    @Schema(name = "regionName", description = "区域名")
//    private String regionName;
//	
//	//@NotNull(message = "请输入小区信息")
//    @Schema(name = "estate", description = "小区信息")
//    private JSONObject estate;
//	
//	//@NotNull(message = "请输入小区名")
//    @Schema(name = "estateName", description = "小区名")
//    private String estateName;
	
    @Schema(name = "addInfo", description = "补充信息")
    private String addInfo;
	
    @Schema(name = "style", description = "风格")
    private String style = "1"; //默认取值
	
//	//@NotNull(message = "请输入政策时间")
//    @Schema(name = "policyTime", description = "政策时间")
//    private String policyTime;
//	
//	//@NotNull(message = "请输入配套")
//    @Schema(name = "facilities", description = "配套")
//    private List<String> facilities;
	
    @Schema(name = "houseInfo", description = "房源")
	private List<JSONObject> houseInfo;
    
    public Map<String, Object> toMap() {
    	Map<String, Object> map = new HashMap<>();
		map.put("userId", userId);
    	map.put("writeType", writeType);
    	map.put("houseBaseInfo", houseBaseInfo);
    	map.put("userInfo", userInfo);
    	map.put("cityName", cityName);
    	map.put("areaName", areaName);
    	map.put("policy", policy);
    	map.put("compareType", compareType);
    	map.put("houseCompareA", houseCompareA);
    	map.put("houseCompareB", houseCompareB);
    	map.put("compareA", compareA);
    	map.put("compareB", compareB);
    	map.put("lifeInfo", lifeInfo);
    	map.put("addInfo", addInfo);
    	map.put("style", style);
    	map.put("houseInfo", houseInfo);
    	return map;
    }
    
    public void checkParams() {
    	if (StringUtils.isBlank(writeType)) throw new BusinessException("bc.cpm.aixg.1012", "文章类型");
    	if (!(writeType.equals("11") || writeType.equals("12") || writeType.equals("13") || writeType.equals("14") || writeType.equals("15") || writeType.equals("16") || writeType.equals("17"))) throw new BusinessException("bc.cpm.aixg.1013", "文章类型");
//    	if (writeType.equals("1") && StringUtils.isBlank(cityName)) throw new BusinessException("bc.cpm.aixg.1012", "城市");
//    	if (writeType.equals("1") && estate == null) throw new BusinessException("bc.cpm.aixg.1012", "小区信息");
//    	if (writeType.equals("1") && StringUtils.isBlank(estateName)) throw new BusinessException("bc.cpm.aixg.1012", "小区");
//    	if (writeType.equals("2") && StringUtils.isBlank(cityName)) throw new BusinessException("bc.cpm.aixg.1012", "城市");
//    	if (writeType.equals("2") && StringUtils.isBlank(regionName)) throw new BusinessException("bc.cpm.aixg.1012", "区域");
//    	if (writeType.equals("3") && (houseInfo == null || houseInfo.size() == 0)) throw new BusinessException("bc.cpm.aixg.1012", "房源");
//    	if (writeType.equals("4") && StringUtils.isBlank(policy)) throw new BusinessException("bc.cpm.aixg.1012", "政策");
//    	if (writeType.equals("5") && StringUtils.isBlank(cityName)) throw new BusinessException("bc.cpm.aixg.1012", "城市");
//    	if (writeType.equals("5") && (facilities == null || facilities.size() == 0)) throw new BusinessException("bc.cpm.aixg.1012", "配套");
    	if (writeType.equals("11") && StringUtils.isBlank(cityName)) throw new BusinessException("bc.cpm.aixg.1012", "城市");
    	if (writeType.equals("11") && StringUtils.isBlank(policy)) throw new BusinessException("bc.cpm.aixg.1012", "政策");
    	if (writeType.equals("12") && StringUtils.isBlank(compareType)) throw new BusinessException("bc.cpm.aixg.1012", "对比类型");
    	if (writeType.equals("12") && !(compareType.equals("1") || compareType.equals("2") || compareType.equals("3"))) throw new BusinessException("bc.cpm.aixg.1012", "对比类型");
    	if (writeType.equals("12") && compareType.equals("1") && houseCompareA == null) throw new BusinessException("bc.cpm.aixg.1012", "对比对象1");
    	if (writeType.equals("12") && compareType.equals("1") && houseCompareB == null) throw new BusinessException("bc.cpm.aixg.1012", "对比对象2");
    	if (writeType.equals("12") && (compareType.equals("2") || compareType.equals("3")) && compareA == null) throw new BusinessException("bc.cpm.aixg.1012", "对比对象1");
    	if (writeType.equals("12") && (compareType.equals("2") || compareType.equals("3")) && compareB == null) throw new BusinessException("bc.cpm.aixg.1012", "对比对象2");
    	if (writeType.equals("13") && (houseInfo == null || houseInfo.size() == 0)) throw new BusinessException("bc.cpm.aixg.1012", "房源");
    	if (writeType.equals("13") && StringUtils.isBlank(lifeInfo)) throw new BusinessException("bc.cpm.aixg.1012", "猜你想说");
    	if (writeType.equals("14") && (houseInfo == null || houseInfo.size() == 0)) throw new BusinessException("bc.cpm.aixg.1012", "房源");
    	if (writeType.equals("15") && StringUtils.isBlank(cityName)) throw new BusinessException("bc.cpm.aixg.1012", "城市");
    	if (writeType.equals("15") && StringUtils.isBlank(areaName)) throw new BusinessException("bc.cpm.aixg.1012", "版块");
    	if (writeType.equals("16") && StringUtils.isBlank(cityName)) throw new BusinessException("bc.cpm.aixg.1012", "城市");
    	if (writeType.equals("16") && StringUtils.isBlank(policy)) throw new BusinessException("bc.cpm.aixg.1012", "政策");
    	if (writeType.equals("17") && houseBaseInfo == null) throw new BusinessException("bc.cpm.aixg.1012", "房源信息");
    	if (writeType.equals("17")) houseBaseInfo.checkParams();
    	if (writeType.equals("17") && userInfo == null) throw new BusinessException("bc.cpm.aixg.1012", "报告个人信息");
    	if (writeType.equals("17")) userInfo.checkParams();
    }
    
    public String getConfigCode() {
    	//11:政策解读专家 12:价值评测专家 13:房产生活专家 14:房源专家 15:月度市场专家 16:行业政策分析 17:价值评测报告
    	switch (writeType) {
    		case "11" : return "POLICY_INTERPRETATION_FLOW";
    		case "12" : switch (compareType) {
    					case "1" : return "HOUSING_COMPARISON_FLOW";
    					case "2" : return "ESTATE_COMPARISON_FLOW";
    					case "3" : return "REGION_AREA_COMPARISON_FLOW";
    				}; break;
    		case "13" : return "ESTATE_LIFE_FLOW";
    		case "14" : return "HOUSING_RESOURCE_FLOW";
    		case "15" : return "MONTHLY_MARKET_ANALYSIS";
    		case "16" : return "ANALYSIS_OF_INDUSTRY_POLICIES";
    		case "17" : return "ASSET_VALUATION_FLOW";
    	}
    	return null;
    }
    
    public String getWriteTypeByConfigCode(String configCode) {
    	//11:政策解读专家 12:价值评测专家 13:房产生活专家 14:房源专家 15:月度市场专家 16:行业政策分析 17:价值评测报告
    	switch (configCode) {
    		case "POLICY_INTERPRETATION_FLOW" : return "11";
    		case "HOUSING_COMPARISON_FLOW" : return "12";
    		case "ESTATE_COMPARISON_FLOW" : return "12";
    		case "REGION_AREA_COMPARISON_FLOW" : return "12";
    		case "ESTATE_LIFE_FLOW" : return "13";
    		case "HOUSING_RESOURCE_FLOW" : return "14";
    		case "MONTHLY_MARKET_ANALYSIS" : return "15";
    		case "ANALYSIS_OF_INDUSTRY_POLICIES" : return "16";
    		case "ASSET_VALUATION_FLOW" : return "17";
    	}
    	return null;
    }
    
    public LaunchFlowPO.RunMode getRunMode() {
    	//11:政策解读专家 12:价值评测专家 13:房产生活专家 14:房源专家 15:月度市场专家 16:行业政策分析 17:价值评测报告
    	switch (writeType) {
    		case "12" : return LaunchFlowPO.RunMode.SYNC;
    		case "17" : return LaunchFlowPO.RunMode.SYNC;
    	}
    	return LaunchFlowPO.RunMode.SYNC;
    }

}
