package com.dcai.aixg.domain.task;

import jakarta.persistence.*;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Entity
@NoArgsConstructor
@Comment("政策")
@Table(name = "tb_policy")
@Where(clause = "logic_delete = 0")
public class Policy {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", columnDefinition = "bigint(20) COMMENT 'ID'")
    private Long id;

    @Column(name = "cityName", columnDefinition = "varchar(255) COMMENT '城市名称'")
    private String cityName;

    @Column(name = "title", columnDefinition = "varchar(255) COMMENT '政策标题'")
    private String title;

}
