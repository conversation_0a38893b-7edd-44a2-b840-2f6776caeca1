package com.dcai.aixg.domain.task;


import com.dcai.aixg.dto.task.MessageDTO;
import com.dcai.aixg.integration.wechat.MessageTemplate;
import com.dcai.aixg.integration.wechat.WechatService;
import com.dcai.message.api.api.MessageAPI;
import com.dcai.message.api.pro.SendMessagePO;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.utils.StringUtils;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@Getter
@Entity
@NoArgsConstructor
@Comment("消息")
@Table(name = "tb_message")
@Where(clause = "logic_delete = 0")
public class Message extends BaseEntity<Message> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", columnDefinition = "bigint(20) COMMENT 'ID'")
    private Long id;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "task_id", columnDefinition = "bigint(20) COMMENT '任务ID'")
    private Task task;

    @Column(name = "open_id", columnDefinition = "varchar(50) COMMENT '微信openId'")
    private String openId;

    @Enumerated(EnumType.STRING)
    @Column(name = "wx_status", nullable = false, columnDefinition = "varchar(50) COMMENT '微信模板消息状态'")
    private MessageDTO.Status wxStatus = MessageDTO.Status.SEND_WAIT;

    @Column(name = "phone", columnDefinition = "varchar(50) COMMENT '手机号'", nullable = false)
    private String phone;

    @Enumerated(EnumType.STRING)
    @Column(name = "node_status", nullable = false, columnDefinition = "varchar(50) COMMENT '短信发送状态'")
    private MessageDTO.Status nodeStatus = MessageDTO.Status.SEND_WAIT;

    public Message(Task task, String openId, String phone) {
        this.task = task;
        this.openId = openId;
        this.phone = phone;
    }

    public void handleSendMessage() {
        MessageTemplate messageTemplate = switch (task.getType()) {
            case WRITE -> MessageTemplate.ARTICLE;
            case REPORT -> MessageTemplate.REPORT;
        };
        String writeId = "";
        if (task instanceof Write write) {
            writeId = write.getWriteId();
        } else if (task instanceof Report report) {
            writeId = report.getReportId();
        }
        if (StringUtils.notBlank(openId)) {
            Boolean wxMessage = getBean(WechatService.class).sendMessage(id, task.getTitle(), task.getCompletionTime(), openId, messageTemplate, writeId);
            wxStatus = wxMessage ? MessageDTO.Status.SEND_SUCC : MessageDTO.Status.SEND_FAIL;
        }
        String uuid = UUID.randomUUID().toString();
        ApiResponse<List<com.dcai.message.api.dto.MessageDTO>> response = getBean(MessageAPI.class).sendMessage(
                new SendMessagePO(
                        "aixg.article_create",
                        uuid,
                        Map.of("phone", phone,"createTime", task.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                )
        );
        log.info("id = {}, response = {}", id, response);
        nodeStatus = response.isFail() ? MessageDTO.Status.SEND_FAIL : MessageDTO.Status.SEND_SUCC;
    }
}
