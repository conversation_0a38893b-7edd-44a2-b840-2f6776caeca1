package com.dcai.aixg.domain.aiagent.node;

import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.domain.aiagent.commons.ProcessBase;
import com.dcai.aixg.domain.aiagent.config.Config4Node;
import com.dcai.aixg.domain.aiagent.flow.Flow;
import com.dcai.aixg.domain.aiagent.functioncall.FunctionCall;
import com.dcai.aixg.domain.commons.ThreadLocalStack;
import com.dcai.aixg.dto.NodeDTO;
import com.dcai.aixg.dto.ProcessDTO;
import com.ejuetc.commons.base.clazz.ClassUtils;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.usertype.JsonUT;
import com.ejuetc.commons.base.usertype.MapContentUT;
import com.ejuetc.commons.base.valueobj.Command;
import com.ejuetc.commons.base.valueobj.MapContent;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import static com.dcai.aixg.domain.aiagent.functioncall.FunctionCall.getRunningFunctionCall;
import static com.dcai.aixg.dto.NodeDTO.Status.*;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("生成任务")
@Table(name = "tb_ai_node")
@Where(clause = "logic_delete = 0")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", columnDefinition = "enum('LLM_GEN','POLICY_COMMENT','SALE_COMPARE','LIFE_EXPERT','HOUSING_EXPERT','MONTHLY_REPORT','INDUSTRY_ANALYST','VALUE_EVALUATION') COMMENT '类型'")
public abstract class Node extends ProcessBase<Node> implements Command {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", columnDefinition = "bigint(20) COMMENT '节点ID'")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "flow_id", columnDefinition = "bigint(20) COMMENT '父工作流ID'")
    private Flow flow;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, insertable = false, updatable = false)
    private NodeDTO.Type type;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "config_id", columnDefinition = "bigint(20) COMMENT '配置ID'")
    private Config4Node config;

    @Type(JsonUT.class)
    @Column(name = "request", columnDefinition = "longtext COMMENT '请求内容'")
    private JSONObject request;

    @Enumerated(EnumType.STRING)
    @Comment("状态")
    @Column(name = "status", nullable = false)
    private NodeDTO.Status status = WAIT;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_node_id", columnDefinition = "bigint(20) COMMENT '父节点ID'")
    private Node parentNode;

    @Setter(AccessLevel.PROTECTED)
    @Column(name = "process_info_1", columnDefinition = "longtext COMMENT '处理过程信息1'")
    private String processInfo1;

    @Setter(AccessLevel.PROTECTED)
    @Column(name = "process_info_2", columnDefinition = "longtext COMMENT '处理过程信息2'")
    private String processInfo2;

    @Setter(AccessLevel.PROTECTED)
    @Column(name = "process_info_3", columnDefinition = "longtext COMMENT '处理过程信息3'")
    private String processInfo3;

    @Type(MapContentUT.class)
    @Column(name = "properties", columnDefinition = "varchar(511) COMMENT '流程属性配置'")
    private MapContent properties = new MapContent();

    @Column(name = "process_flow_id", columnDefinition = "bigint(20) COMMENT '初始流程id'")
    private Long processFlowId;

    private final static ThreadLocalStack<Long> runningIdStack = new ThreadLocalStack<>();

    protected Node(Config4Node config, JSONObject request, Flow flow) {
        this.request = request;
        this.config = config;
        this.flow = flow;
        this.processFlowId = flow.getOriginationFlowId().getId();
        this.parentNode = getRunningGenerate();
        this.properties = config.getProperties();
        FunctionCall functionCall = getRunningFunctionCall();
        if (functionCall != null) functionCall.setChildNode(this);
    }

    public static Node newGenerate(Config4Node config, JSONObject request) {
        return newGenerate(config, request, null);
    }

    public static Node newGenerate(Config4Node config, Flow flow) {
        return newGenerate(config, flow.getRequest(), flow);
    }

    private static Node newGenerate(Config4Node config, JSONObject request, Flow flow) {
        return switch (config.getNodeType()) {
            case LLM_GEN -> new Node4LlmGen(config, request, flow);
            case SALE_COMPARE -> new Node4SaleCompare(config, request, flow);
            case POLICY_COMMENT -> new Node4PolicyComment(config, request, flow);
            case LIFE_EXPERT -> new Node4LifeExpert(config, request, flow);
            case HOUSING_EXPERT -> new Node4HousingExpert(config, request, flow);
            case MONTHLY_REPORT -> new Node4MonthlyMarket(config, request, flow);
            case INDUSTRY_ANALYST -> new Node4IndustryPolicies(config, request, flow);
            case VALUE_EVALUATION -> new Node4ValueEvaluation(config, request, flow);
        };
    }

    /**
     * 执行生成任务
     */
    public void exec() {
        if (isCompleted()) {
            log.warn("生成任务已完成，无需重复执行: {}", getId());
            return;
        }

        try {
            log.info("执行生成任务: {}", id);
            timerStart();
            runningIdStack.push(id);
            this.status = doExec();
        } catch (Exception e) {
            log.error("生成任务执行失败: {}", id, e);
            this.setErrorMessage(e.getMessage());
            this.status = FAILED;
            if (flow != null) flow.makeCompleted(this);
        } finally {
            if (isCompleted()) timerStop();
            Long popId = runningIdStack.pop();
            if (!id.equals(popId))
                log.error("堆栈弹出异常: 预期id({}),实际id({})", id, popId);
        }
    }


    /**
     * 是否完成
     */
    public boolean isCompleted() {
        return status == SUCCESS ||
               status == NodeDTO.Status.FAILED ||
               status == NodeDTO.Status.CANCELLED;
    }

    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return status == SUCCESS;
    }

    public NodeDTO.Type getType() {
        if (type == null)
            type = NodeDTO.Type.valueOf(ClassUtils.getClassAnnotation(this.getClass(), DiscriminatorValue.class).value());
        return type;
    }

    public static Node getRunningGenerate() {
        Long generateId = runningIdStack.peek();
        return generateId != null ? getBean(NodeRpt.class).getReferenceById(generateId) : null;
    }

    public String getPrevGenerateResponse() {
        if (flow == null) throw new CodingException("生成过程[%s]不关联任何工作流,无法获取前序生成应答!", getId());
        return flow.getPrevGenerateResponse(this);
    }

    public void receiveAsyncResponse(NodeDTO.Status status, String response) {
        if (this.status != GENERATING) {
            log.warn("重复收到预处理结果: {}", response);
            return;
        }
        log.info("收到预处理结果: {}", response);
        this.setResponse(response);
        this.status = status;
        timerStop();
    }

    public Long getFlowId() {
        return flow != null ? flow.getId() : null;
    }

    protected abstract NodeDTO.Status doExec() throws Exception;

    public int getResponseLength() {
        return getResponse() != null ? getResponse().length() : 0;
    }

    public boolean isHasFlow() {
        return flow != null;
    }

    public boolean isHasParentFlow() {
        return flow != null && flow.hasParentFlow();
    }

    public Flow getParentFlow() {
        return flow.getParentFlow();
    }

    @Override
    public ProcessDTO.Type getProcessType() {
        return ProcessDTO.Type.GENERATE;
    }

    public boolean isConfigCode(String configCode) {
        return config != null && configCode.equals(config.getCode());
    }
}
