package com.dcai.aixg.domain.task;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.domain.aiagent.flow.Flow;
import com.dcai.aixg.domain.broker.Broker;
import com.dcai.aixg.dto.task.TaskDTO;
import com.dcai.aixg.dto.task.WriteCreateDTO;
import com.dcai.aixg.dto.task.WriteDetailDTO;
import com.dcai.aixg.pro.task.WriteCreatePO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.community.BindCommunityRO;
import com.ejuetc.consumer.api.community.CommunityAPI;

import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Getter
@Entity
@Slf4j
@DiscriminatorValue("WRITE")
@SubtypeCode(parent = Task.class, code = "WRITE", name = "文章")
@Accessors(chain = true)
@NoArgsConstructor
public class Write extends Task {

    @Column(name = "write_id", columnDefinition = "varchar(255) COMMENT '写作ID'")
    private String writeId;

    @Column(name = "article_id", columnDefinition = "varchar(255) COMMENT '文章ID'")
    private String articleId;
    
    public Write(Write parent, TaskDTO.SubType subType) {
    	this.parent = parent;
    	this.subType = subType;
    }

    public Write(Broker broker, WriteCreateDTO dto, WriteCreatePO po) {
        this.broker = broker;
        this.writeType = po.getWriteType();
        this.ask = po.getAddInfo();
        this.writeId = dto.getWriteId();
        this.articleId = dto.getArticleId();
        this.topic = dto.getTopic();
        this.title = dto.getTitle();
    }

    public Write(Broker broker, WriteCreatePO po) {
        this.broker = broker;
        this.writeType = po.getWriteType();
        this.ask = po.getAddInfo();
        if (po.getHouseInfo() != null && po.getHouseInfo().size() > 0) {
            List<String> houseIds = po.getHouseInfo().stream().map(house -> house.getString("id")).collect(Collectors.toList());
            this.houseIds = houseIds;
            this.houseInfos = po.getHouseInfo().stream().map(house -> house.toString()).collect(Collectors.toList());
            //this.houseInfos = po.getHouseInfo();
        }
    }
    
    public Write(Broker broker, Flow flow) {
        this.broker = broker;
        this.writeType = new WriteCreatePO().getWriteTypeByConfigCode(flow.getRootConfig().getCode());
        this.ask = flow.getRequest().toJSONString();
        String response = flow.getResponse();
		JSONObject jsonObject = JSONObject.parseObject(response);
        this.title = jsonObject.getString("title");
        this.content = response;
        this.subType = TaskDTO.SubType.ORIGIN;
        this.status = TaskDTO.Status.DONE;
        this.flowId = flow.getId();
        this.openId = broker.getMessageOpenId();
        this.completionTime = LocalDateTime.now();
    }

    public void writeSubmit(WriteCreateDTO dto, Long flowId) {
        this.writeId = dto.getWriteId();
        this.articleId = dto.getArticleId();
        this.topic = dto.getTopic();
        this.title = dto.getTitle();
        this.flowId = flowId;
    }

    public String getEstateDistrict(String estateAddress, String estateName) {
//        String saasKeyCode = getProperty("ejuetc.saasApi.keyCode");
//        String saasKeySecret = getProperty("ejuetc.saasApi.keySecret");
//        String saasHost = getProperty("ejuetc.saasApi.url");
//        String consumerUrl = getProperty("ejuetc.consumer.url");
//    	SaaSApiSDK sdk = new SaaSApiSDK(saasKeyCode, saasKeySecret, saasHost);
//        CommunityAPI communityAPI = sdk.feignClient(CommunityAPI.class, consumerUrl);
        ApiResponse<BindCommunityRO> bind = getBean(CommunityAPI.class).bind(estateAddress, estateName);
        if (!bind.isSucc() || bind.getData() == null) return null;
        return bind.getData().getDistrict();
    }

}
