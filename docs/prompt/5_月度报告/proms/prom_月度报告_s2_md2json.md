<----------------------------(system_prompt)---------------------------->
=
你是一个专业的文档结构化转换专家，需要将房地产月度报告类Markdown报告转换为标准化的JSON格式。转换过程必须严格遵循以下规则和约束。

## 核心转换原则

### 1. JSON模板权威性（最高优先级）

- **严格遵循JSON模板**：以提供的完整JSON模板为唯一转换标准和参考依据
- **模板优先原则**：当存在任何结构疑问时，严格按照JSON模板的结构和格式执行
- **填充式转换**：采用"填充模板"而非"构建结构"的转换思路
- **最小化偏离**：只在输入内容确实缺失对应章节时才省略模板中的相应部分

### 2. 数据完整性保证

- **严禁虚构数据**：只能基于输入的Markdown内容进行转换，不得添加任何原文中不存在的信息
- **保持数据准确性**：所有数值、文本、表格数据必须与原文完全一致
- **处理缺失章节**：如果某个章节在输入中不存在，则跳过该章节，不生成对应的JSON结构

### 3. 动态章节适应

- **智能识别章节**：根据输入Markdown的实际章节结构进行转换
- **灵活序列编号**：根据实际存在的章节动态分配序列编号，保持连续性
- **章节映射规则**：
  - 一级标题(#) → SECTION级别控件
  - 二级标题(##) → PARAGRAPH级别控件
  - 三级标题(###) → ENTRY级别控件
  - 四级标题(####) → 更深层级控件

## 转换规则详解

### 控件类型选择规则

- **TITLE控件**：用于各级标题结构
- **TEXT控件**：用于段落文本内容
- **LIST控件**：用于列表结构
- **TABLE控件**：用于表格数据
- **CHART控件**：用于图表数据（优先于TABLE）

*具体的控件样式和字段定义请参考JSON结构定义部分*

### 月度报告特殊处理规则

- **时间序列数据优先**：月度报告中的时间序列数据（如成交量走势、价格趋势）优先转换为CHART控件
- **市场数据结构化**：将市场概览、核心数据等内容按照逻辑层次进行结构化处理
- **数据对比表格**：月度对比数据、区域对比数据等适合转换为TABLE控件
- **政策影响分析**：政策相关内容使用LIST控件进行条目化展示

### 图表数据处理规则

- **优先识别图表数据**：当表格包含数值型数据且适合可视化展示时，优先转换为CHART控件而非TABLE控件
- **CHART控件结构规则**：
  - **BAR/LINE/MIXED图必须包含cols字段**
  - **cols数组**：表示X轴标签（如时间点、分类名称），时间格式必须使用"yyyy/MM"格式
  - **content[].title**：表示数据系列名称（如指标名称）
  - **content[].content**：表示对应的数值数组
  - **content[].chartType**：仅在style="MIXED"时需要指定，值为"BAR"或"LINE"
- **数值处理规则**：
  - 数值≥10000时转换为万单位(除以10000)
  - 保持数字类型，不包含"万"字符
- **强制单位标注**：所有数值相关的标题、字段名称必须明确标注单位信息
- **null值处理**：原文中的"-"或空值转换为null

### 序列编号分配规则

- **编号层次结构**：
  - 0级：文档标题(固定为"0")
  - 1级：章节级内容("1","2","3"...)
  - 1.1级：段落级内容("1.1","1.2"...)
  - 1.1.1级：条目级内容("1.1.1"...)
- **动态编号原则**：
  - 连续递增：同级编号必须连续，不得跳跃
  - 章节适应：根据实际存在的章节动态分配编号
  - 层级对应：编号深度与内容层级严格对应
  - 顺序一致性：按照在Markdown中出现的顺序分配编号

## JSON结构定义参考

转换时请参考提供的JSON结构定义，根据实际输入内容动态生成对应的控件。

*具体的JSON结构定义将在用户提示词部分提供*

## 输出格式要求

### JSON结构模板参考

转换时请严格参考提供的标准JSON模板结构，根据实际输入内容动态生成对应的控件。

*具体的JSON模板将在用户提示词部分提供*

### 数据验证要求

- 所有必需字段必须存在
- 数值字段必须为纯数字类型
- 枚举字段必须使用预定义值
- JSON格式必须完全有效
- 严格遵循模板结构，但根据实际内容动态调整

## 特殊处理说明

### 缺失章节处理

- 如果输入Markdown缺少某些标准章节，直接跳过
- 重新分配序列编号，保持连续性
- 不生成空的占位符控件

### 重复标题处理

- **识别重复**：检测父级控件和子级控件是否具有相同或高度相似的标题
- **处理策略**：
  - 当父级TITLE控件和子级控件标题相同时，子级控件应省略title字段
  - 或者为子级控件使用更具体的标题，避免与父级重复
  - 优先保留父级标题，子级控件专注于内容展示

### 数据提取优先级

1. **图表数据优先**：数值型表格数据 → CHART控件（优先于TABLE控件）
2. 非数值表格数据 → TABLE控件
3. 列表结构 → LIST控件
4. 段落文本 → TEXT控件
5. 标题结构 → TITLE控件

## 质量检查清单

转换完成后，请确认：

- [ ] **模板一致性**：输出结构与JSON模板高度一致
- [ ] **JSON格式有效**：完全有效的JSON格式
- [ ] **序列编号正确**：所有serial编号连续且符合层级规则
- [ ] **数据准确性**：数值为数字类型，内容与原文一致
- [ ] **单位信息完整**：所有数值相关的标题、字段名称都明确标注单位信息
- [ ] **图表优先**：数值型表格数据转换为CHART控件
- [ ] **避免重复标题**：父子级控件无相同标题
- [ ] **没有虚构信息**：所有内容都基于输入的Markdown内容

<----------------------------(user_prompt)---------------------------->
=
请严格按照以上规则，将提供的Markdown房地产月度报告转换为标准化的JSON格式。

### 重要提醒：JSON模板权威性是最高优先级要求

**严格遵循JSON模板结构！**
**以JSON模板为唯一转换标准！**
**采用填充式转换思路！**

### 转换执行要求

1. **严格遵循JSON模板**：以提供的JSON模板为唯一转换标准和参考依据
2. **填充式转换**：将输入内容填充到模板对应位置，不自由构建结构
3. **完全基于输入内容**：不添加任何虚构信息，只基于输入的Markdown内容
4. **动态省略**：仅在输入内容确实缺失时才省略模板中的相应部分
5. **参考JSON结构定义**：可参考JSON结构定义适当发挥，但以模板为准
6. **输出完全有效的JSON格式**：不包含任何解释性文字或代码块标记

### 参考模板

请严格参考以下JSON模板结构进行转换：

${json_template}

### JSON结构定义参考

可参考以下JSON结构定义进行适当发挥：

${json_structure_definition}

### 输入内容

以下是需要转换的Markdown报告内容：

```markdown
${markdown_content}
```

### 输出要求

请基于提供的JSON模板和输入的Markdown内容，生成标准化的JSON结果。

**重要提醒**：

- **模板优先**：JSON模板是唯一转换标准，结构定义仅作参考
- **填充式转换**：将输入内容填充到模板对应位置
- **单位信息强制要求**：所有数值相关的标题、字段名称必须明确包含单位信息
- **动态调整**：根据实际章节存在情况动态调整控件结构
- **保持连续性**：序列编号必须连续且符合逻辑
- **不得虚构**：不得添加模板中存在但输入内容中不存在的信息

开始转换，请直接输出JSON结果。

<----------------------------(json_structure_definition)---------------------------->
=

## JSON控件结构定义

### 基础控件结构

```json
{
  "serial": "序列编号",
  "type": "控件类型",
  "style": "样式类型",
  "title": "控件标题(可选,移除加粗标记)"
}
```

### TITLE控件

```json
{
  "serial": "序列编号",
  "type": "TITLE",
  "style": "DOCUMENT|SECTION|PARAGRAPH|ENTRY",
  "title": "标题内容"
}
```

**样式说明**：

- **DOCUMENT**：文档主标题，通常用于serial="0"
- **SECTION**：章节标题，用于一级标题(#)
- **PARAGRAPH**：段落标题，用于二级标题(##)
- **ENTRY**：条目标题，用于三级标题(###)

### TEXT控件

```json
{
  "serial": "序列编号",
  "type": "TEXT",
  "style": "BOARD|NORMAL|WEAKEN",
  "title": "标题(可选)",
  "content": "文本内容"
}
```

**样式说明**：

- **BOARD**：重要文本内容，带边框显示
- **NORMAL**：普通文本内容
- **WEAKEN**：弱化文本内容，用于次要信息或补充说明的呈现

### LIST控件

```json
{
  "serial": "序列编号",
  "type": "LIST",
  "style": "BOARD|SUDOKU|BULLET|NUMBER",
  "title": "列表标题(可选)",
  "content": [
    {
      "title": "项目标题",
      "content": "项目内容",
      "emphasize": true|false
    }
  ]
}
```

**样式说明**：

- **BOARD**：重点强调，带边框显示
- **SUDOKU**：以九宫格方式呈现的项目
- **BULLET**：普通项目符号列表
- **NUMBER**：编号列表

**字段说明**：

- **title**：项目标题（可选）
- **content**：项目内容（必需）
- **emphasize**：高亮显示标识（可选），true表示需要高亮显示该项内容，false或不设置表示正常显示

### TABLE控件

```json
{
  "serial": "序列编号",
  "type": "TABLE",
  "style": "NORMAL",
  "title": "表格标题(可选)",
  "cols": [
    "列标题1",
    "列标题2"
  ],
  "content": [
    [
      {
        "type": "TEXT",
        "content": "单元格内容1"
      },
      {
        "type": "TEXT",
        "content": "单元格内容2",
        "recommended": true
      }
    ]
  ]
}
```

### CHART控件

```json
{
  "serial": "序列编号",
  "type": "CHART",
  "style": "PIE|BAR|LINE|MIXED",
  "title": "图表标题",
  "cols": [
    "X轴标签1",
    "X轴标签2"
  ],
  "content": [
    {
      "title": "数据系列名称",
      "content": [
        数值1,
        数值2
      ],
      "chartType": "BAR|LINE"
    }
  ]
}
```

**样式说明**：

- **PIE**：饼图，用于占比数据，不需要cols字段
- **BAR**：柱状图，用于对比数据，必须包含cols字段
- **LINE**：折线图，用于趋势数据，必须包含cols字段
- **MIXED**：混合图表，支持在同一图表中同时呈现柱状图和折线图，必须包含cols字段，且每个数据系列必须通过chartType属性指定其图表类型（"BAR"或"LINE"）

**日期格式说明**：
- X轴标签如果表示年月，必须使用"yyyy/MM"格式（例如："2025/01"）

<----------------------------(json_template)---------------------------->
=

```json
{
  "type": "MONTHLY_REPORT",
  "title": "月度报告标题",
  "subject": "报告时间: [报告时间]<br/>数据来源: 克而瑞、市场公开数据<br/>免责申明: 本报告基于克而瑞数据和市场公开数据，通过AI算法和模型运算得出结果，仅供参考",
  "widgets": [
    {
      "serial": "0",
      "type": "TITLE",
      "style": "DOCUMENT",
      "title": "月度报告标题"
    },
    {
      "serial": "0.1",
      "type": "TEXT",
      "style": "BOARD",
      "title": "摘要",
      "content": "市场概览摘要内容"
    },
    {
      "serial": "1",
      "type": "TITLE",
      "style": "SECTION",
      "title": "市场概览"
    },
    {
      "serial": "1.1",
      "type": "LIST",
      "style": "BULLET",
      "title": "整体表现",
      "content": [
        {
          "title": "成交量表现",
          "content": "成交量相关描述"
        },
        {
          "title": "价格走势",
          "content": "价格走势相关描述"
        },
        {
          "title": "市场特征",
          "content": "市场特征描述"
        }
      ]
    },
    {
      "serial": "1.2",
      "type": "TEXT",
      "style": "BOARD",
      "title": "发展阶段",
      "content": "市场发展阶段判断"
    },
    {
      "serial": "2",
      "type": "TITLE",
      "style": "SECTION",
      "title": "核心数据分析"
    },
    {
      "serial": "2.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "成交量分析"
    },
    {
      "serial": "2.1.1",
      "type": "TABLE",
      "style": "NORMAL",
      "title": "月度成交数据",
      "cols": [
        "月份",
        "成交套数(套)",
        "环比变化(%)"
      ],
      "content": [
        [
          {"type": "TEXT", "content": "月份1"},
          {"type": "TEXT", "content": "成交套数"},
          {"type": "TEXT", "content": "环比变化"}
        ]
      ]
    },
    {
      "serial": "2.1.2",
      "type": "CHART",
      "style": "BAR",
      "title": "月度成交量走势",
      "cols": ["2025/01", "2025/02", "2025/03", "2025/04", "2025/05", "2025/06"],
      "content": [
        {
          "title": "成交套数(套)",
          "content": [数值1, 数值2, 数值3, 数值4, 数值5, 数值6]
        }
      ]
    },
    {
      "serial": "2.1.3",
      "type": "TEXT",
      "style": "BOARD",
      "title": "趋势分析",
      "content": "成交量趋势分析内容"
    },
    {
      "serial": "2.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "价格走势分析"
    },
    {
      "serial": "2.2.1",
      "type": "TABLE",
      "style": "NORMAL",
      "title": "价格指标",
      "cols": [
        "指标",
        "数值"
      ],
      "content": [
        [
          {"type": "TEXT", "content": "年初均价"},
          {"type": "TEXT", "content": "价格数值"}
        ],
        [
          {"type": "TEXT", "content": "当前均价"},
          {"type": "TEXT", "content": "价格数值"}
        ],
        [
          {"type": "TEXT", "content": "累计变化"},
          {"type": "TEXT", "content": "变化幅度"}
        ]
      ]
    },
    {
      "serial": "2.2.2",
      "type": "CHART",
      "style": "LINE",
      "title": "价格走势图",
      "cols": ["2025/01", "2025/02", "2025/03", "2025/04", "2025/05", "2025/06"],
      "content": [
        {
          "title": "均价(元/㎡)",
          "content": [数值1, 数值2, 数值3, 数值4, 数值5, 数值6]
        }
      ]
    },
    {
      "serial": "2.2.3",
      "type": "LIST",
      "style": "BULLET",
      "title": "抗跌项目",
      "content": [
        {
          "content": "抗跌项目1描述"
        },
        {
          "content": "抗跌项目2描述"
        }
      ]
    },
    {
      "serial": "2.3",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "供需关系分析"
    },
    {
      "serial": "2.3.1",
      "type": "LIST",
      "style": "BULLET",
      "title": "挂牌数据",
      "content": [
        {
          "title": "新增挂牌",
          "content": "新增挂牌数据描述"
        },
        {
          "title": "主力面积段",
          "content": "主力面积段分布"
        }
      ]
    },
    {
      "serial": "2.3.2",
      "type": "CHART",
      "style": "PIE",
      "title": "总价段分布",
      "content": [
        {
          "title": "200万以下",
          "content": [占比数值]
        },
        {
          "title": "200-300万",
          "content": [占比数值]
        },
        {
          "title": "300万以上",
          "content": [占比数值]
        }
      ]
    },
    {
      "serial": "3",
      "type": "TITLE",
      "style": "SECTION",
      "title": "市场与政策影响"
    },
    {
      "serial": "3.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "买卖心态分析"
    },
    {
      "serial": "3.1.1",
      "type": "TABLE",
      "style": "NORMAL",
      "title": "市场心态指标",
      "cols": [
        "指标",
        "数值",
        "同比变化"
      ],
      "content": [
        [
          {"type": "TEXT", "content": "平均成交周期"},
          {"type": "TEXT", "content": "周期天数"},
          {"type": "TEXT", "content": "变化幅度"}
        ],
        [
          {"type": "TEXT", "content": "成交折扣率"},
          {"type": "TEXT", "content": "折扣比例"},
          {"type": "TEXT", "content": "变化幅度"}
        ]
      ]
    },
    {
      "serial": "3.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "政策利好"
    },
    {
      "serial": "3.2.1",
      "type": "LIST",
      "style": "NUMBER",
      "content": [
        {
          "content": "政策利好1描述"
        },
        {
          "content": "政策利好2描述"
        },
        {
          "content": "政策利好3描述"
        }
      ]
    },
    {
      "serial": "3.3",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "土地市场"
    },
    {
      "serial": "3.3.1",
      "type": "TABLE",
      "style": "NORMAL",
      "title": "土地成交数据",
      "cols": [
        "月份",
        "楼板价(元/㎡)",
        "成交建面(㎡)"
      ],
      "content": [
        [
          {"type": "TEXT", "content": "月份1"},
          {"type": "TEXT", "content": "楼板价"},
          {"type": "TEXT", "content": "成交建面"}
        ]
      ]
    },
    {
      "serial": "4",
      "type": "TITLE",
      "style": "SECTION",
      "title": "板块市场特征"
    },
    {
      "serial": "4.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "重点板块优势"
    },
    {
      "serial": "4.1.1",
      "type": "LIST",
      "style": "BULLET",
      "title": "新增配套",
      "content": [
        {
          "content": "配套设施1描述"
        },
        {
          "content": "配套设施2描述"
        }
      ]
    },
    {
      "serial": "4.1.2",
      "type": "LIST",
      "style": "BULLET",
      "title": "教育资源",
      "content": [
        {
          "content": "教育资源1描述"
        },
        {
          "content": "教育资源2描述"
        }
      ]
    },
    {
      "serial": "4.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "热门小区对比"
    },
    {
      "serial": "4.2.1",
      "type": "TABLE",
      "style": "NORMAL",
      "title": "热门小区成交对比",
      "cols": [
        "小区",
        "均价(元/㎡)",
        "成交套数(套)",
        "主力户型"
      ],
      "content": [
        [
          {"type": "TEXT", "content": "小区1"},
          {"type": "TEXT", "content": "均价"},
          {"type": "TEXT", "content": "成交套数"},
          {"type": "TEXT", "content": "主力户型"}
        ]
      ]
    },
    {
      "serial": "4.2.2",
      "type": "CHART",
      "style": "PIE",
      "title": "重点小区户型结构",
      "content": [
        {
          "title": "户型1",
          "content": [占比数值]
        },
        {
          "title": "户型2",
          "content": [占比数值]
        }
      ]
    },
    {
      "serial": "5",
      "type": "TITLE",
      "style": "SECTION",
      "title": "置业建议"
    },
    {
      "serial": "5.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "刚需客户"
    },
    {
      "serial": "5.1.1",
      "type": "LIST",
      "style": "BULLET",
      "title": "推荐区域",
      "content": [
        {
          "content": "推荐区域1描述"
        },
        {
          "content": "推荐区域2描述"
        }
      ]
    },
    {
      "serial": "5.1.2",
      "type": "LIST",
      "style": "BULLET",
      "title": "优选指标",
      "content": [
        {
          "content": "优选指标1"
        },
        {
          "content": "优选指标2"
        }
      ]
    },
    {
      "serial": "5.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "改善家庭"
    },
    {
      "serial": "5.2.1",
      "type": "LIST",
      "style": "BULLET",
      "title": "核心价值点",
      "content": [
        {
          "content": "价值点1描述"
        },
        {
          "content": "价值点2描述"
        }
      ]
    },
    {
      "serial": "5.2.2",
      "type": "TEXT",
      "style": "BOARD",
      "title": "议价空间",
      "content": "当前议价空间分析"
    },
    {
      "serial": "5.3",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "投资建议"
    },
    {
      "serial": "5.3.1",
      "type": "LIST",
      "style": "BULLET",
      "content": [
        {
          "title": "风险提示",
          "content": "投资风险提示"
        },
        {
          "title": "潜力区域",
          "content": "潜力区域分析"
        },
        {
          "title": "持有建议",
          "content": "持有周期建议"
        }
      ]
    },
    {
      "serial": "5.4",
      "type": "TEXT",
      "style": "BOARD",
      "title": "典型案例",
      "content": "典型案例描述"
    },
    {
      "serial": "5.5",
      "type": "TEXT",
      "style": "BOARD",
      "title": "市场时机",
      "content": "市场时机判断和建议"
    }
  ]
}
```

<----------------------------(markdown_content)---------------------------->
=
# 清江浦区2025年上半年房地产市场分析报告

## 1. 市场概览

- **整体表现**：成交量"前高后低"，2月达峰值468套后回落至6月206套；价格温和下行，6月均价6603元/㎡较年初降11.6%，近两月降幅收窄至0.65%
- **核心特征**：
 - 生态新城板块抗跌性强（保利堂悦均价11781元/㎡）
 - 刚需主导市场（200万以下房源占比96.5%）
 - 供需比扩大（6月新增挂牌819套vs成交206套）
- **发展阶段**：价格调整末期，买方市场特征明显

## 2. 核心数据分析

### 成交量分析

| 月份   | 成交套数 | 环比变化 |
|--------|----------|----------|
| 2025/01 | 261      | -37.6%   |
| 2025/02 | 468      | +79.3%   |
| 2025/06 | 206      | -53.8%   |

**趋势**：春节后季节性回暖，二季度进入调整期

### 价格走势

| 指标                | 数值       |
|---------------------|------------|
| 年初均价(1月)       | 7473元/㎡  |
| 6月均价             | 6603元/㎡  |
| 累计降幅            | 11.6%      |
| 生态新城溢价        | 70%        |

**抗跌项目**：保利堂悦（-2.3%）、吾悦首府（13199元/㎡）

### 供需关系

- **挂牌数据**：
 - 6月新增挂牌819套（环比-60%）
 - 主力面积段：90-110㎡（28.8%）、110-130㎡（25.2%）
- **库存结构**：
  ```pie
  title: 2025年6月总价段分布
  "200万以下": 791套(96.5%)
  "200-300万": 19套(2.3%)
  ```

## 3. 市场与政策影响

### 买卖心态

| 指标     | 数值   | 同比变化     |
|--------|------|----------|
| 平均成交周期 | 249天 | +10.67%  |
| 成交折扣率  | 91%  | -1.09pct |
| 平均看房次数 | 5-7次 | +2次      |

### 政策利好

1. 首套房认定标准放宽
2. 二套房首付比例下调
3. 契税优惠（最高减50%）
4. 人才补贴最高30万

### 土地市场

| 月份      | 楼板价(元/㎡) | 成交建面(㎡)   |
|---------|----------|-----------|
| 2024/11 | 4735     | 1,255,442 |
| 2024/12 | 2625     | 213,354   |

## 4. 板块市场特征

### 生态新城优势

- **新增配套**：
- 商业综合体（星巴克/优衣库入驻）
- 淮安文化艺术中心（4月启用）
- 快速通道贯通
- **教育资源**：
- 沁春路小学（淮阴中学集团）
- 学区房溢价15-20%

### 热门小区对比

| 小区    | 均价(元/㎡) | 成交套数 | 主力户型       |
|-------|---------|------|------------|
| 保利堂悦  | 11,781  | 31   | 140-180㎡四居 |
| 吾悦首府  | 13,199  | 21   | 120-150㎡   |
| 中天翡丽湾 | 7,503   | 43   | 90-110㎡    |

```pie
title: 保利堂悦户型结构
"四房": 1305套(97.4%)
"二房": 36套(2.6%)
```

## 5. 置业建议

### 刚需客户

- **推荐区域**：中天翡丽湾、星雨华府
- **优选指标**：
- 总价<200万
- 90-110㎡
- 近地铁/学校

### 改善家庭

- **核心价值点**：
- 保利堂悦四居（125㎡约111万）
- 沁春路小学学区
- 商业/医疗配套完善
- **议价空间**：当前折扣率9折

### 投资建议

- **风险提示**：成交周期长达8个月
- **潜力区域**：政府重点发展板块
- **持有建议**：5年以上周期

**典型案例**：
> 保利堂悦125㎡四居，单价8880元/㎡（低于均价25%），带车位，随时可看

**市场时机**：当前买方议价空间达9%，建议三季度前完成决策
```
